// lib/premium-manager.js
// Premium Feature Management System for Stashy Extension

/**
 * Premium Feature Manager
 * Handles feature locking/unlocking based on user's premium status
 */
window.StashyPremium = (function() {
    'use strict';

    // Premium feature configuration
    const PREMIUM_FEATURES = {
        // Export features
        EXPORT_PDF: 'export_pdf',
        EXPORT_WORD: 'export_word',
        EXPORT_HTML: 'export_html',
        
        // Google Drive sync
        DRIVE_SYNC: 'drive_sync',
        DRIVE_BACKUP: 'drive_backup',
        
        // Template features
        TEMPLATE_BUILDER: 'template_builder',
        ADVANCED_TEMPLATES: 'advanced_templates',
        CUSTOM_TEMPLATES: 'custom_templates',
        
        // Highlighting features
        ADVANCED_HIGHLIGHTS: 'advanced_highlights',
        HIGHLIGHT_STYLES: 'highlight_styles',
        HIGHLIGHT_CATEGORIES: 'highlight_categories',
        
        // AI features
        AI_FEATURES: 'ai_features',
        AI_ANALYSIS: 'ai_analysis',
        AI_SUMMARIZATION: 'ai_summarization',
        AI_ACADEMIC_SOLVER: 'ai_academic_solver',
        AI_TRANSCRIPT_ANALYSIS: 'ai_transcript_analysis',
        AI_SHOPPING_ASSISTANT: 'ai_shopping_assistant',
        
        // Advanced features
        UNLIMITED_NOTES: 'unlimited_notes',
        UNLIMITED_NOTEBOOKS: 'unlimited_notebooks',
        ADVANCED_SEARCH: 'advanced_search',
        BULK_OPERATIONS: 'bulk_operations'
    };

    // Free tier limitations
    const FREE_LIMITS = {
        MAX_NOTES: 10,
        MAX_NOTEBOOKS: 2,
        MAX_HIGHLIGHTS_PER_PAGE: 5
    };

    // Current premium status (cached)
    let currentPremiumStatus = {
        isPremium: false,
        expiryDate: null,
        lastChecked: 0
    };

    // Trial status tracking
    let currentTrialStatus = {
        isTrialActive: false,
        trialStartDate: null,
        trialExpiryDate: null,
        trialUsed: false,
        trialActivatedAt: null
    };

    // Trial configuration
    const TRIAL_CONFIG = {
        DURATION_DAYS: 7,
        DURATION_MS: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
        NOTIFICATION_THRESHOLDS: {
            THREE_DAYS: 3 * 24 * 60 * 60 * 1000, // 3 days before expiry
            ONE_DAY: 1 * 24 * 60 * 60 * 1000,    // 1 day before expiry
            EXPIRED: 0                            // On expiry
        }
    };

    // Feature usage counters for free tier
    let usageCounters = {
        notes: 0,
        notebooks: 0,
        highlights: 0
    };

    /**
     * Initialize the premium manager
     */
    async function initialize() {
        try {
            // Load cached premium status
            const cached = await chrome.storage.local.get('stashy_premium_status');
            if (cached.stashy_premium_status) {
                currentPremiumStatus = cached.stashy_premium_status;
            }

            // Load trial status
            const trialData = await chrome.storage.local.get('stashy_trial_status');
            if (trialData.stashy_trial_status) {
                currentTrialStatus = trialData.stashy_trial_status;
                // Validate trial expiry
                await validateTrialStatus();
            }

            // Load usage counters
            const counters = await chrome.storage.local.get('stashy_usage_counters');
            if (counters.stashy_usage_counters) {
                usageCounters = counters.stashy_usage_counters;
            }

            // Check premium status if cache is old (> 5 minutes)
            const now = Date.now();
            if (now - currentPremiumStatus.lastChecked > 300000) {
                await refreshPremiumStatus();
            }

            console.log('Stashy Premium: Manager initialized', {
                premium: currentPremiumStatus,
                trial: currentTrialStatus,
                usage: usageCounters
            });
        } catch (error) {
            console.error('Stashy Premium: Error initializing manager:', error);
        }
    }

    /**
     * Check if user has premium access (including active trial)
     * @returns {boolean} True if user is premium or has active trial
     */
    function isPremium() {
        return currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive;
    }

    /**
     * Check if user has active trial
     * @returns {boolean} True if trial is active
     */
    function isTrialActive() {
        return currentTrialStatus.isTrialActive;
    }

    /**
     * Check if user has used their trial
     * @returns {boolean} True if trial has been used
     */
    function isTrialUsed() {
        return currentTrialStatus.trialUsed;
    }

    /**
     * Check if a specific feature is available
     * @param {string} feature - Feature identifier from PREMIUM_FEATURES
     * @returns {boolean} True if feature is available
     */
    function isFeatureAvailable(feature) {
        console.log(`Stashy Premium: Checking feature availability for '${feature}'`);
        console.log(`Stashy Premium: Current status - isPremium: ${currentPremiumStatus.isPremium}, isTrialActive: ${currentTrialStatus.isTrialActive}, usage: ${JSON.stringify(usageCounters)}`);

        // Premium users have access to all features
        if (currentPremiumStatus.isPremium) {
            console.log(`Stashy Premium: Feature '${feature}' available - premium user`);
            return true;
        }

        // Trial users have access to all premium features
        if (currentTrialStatus.isTrialActive) {
            console.log(`Stashy Premium: Feature '${feature}' available - trial user`);
            return true;
        }

        // Free users have limited access
        let isAvailable = false;
        switch (feature) {
            case PREMIUM_FEATURES.UNLIMITED_NOTES:
                isAvailable = usageCounters.notes < FREE_LIMITS.MAX_NOTES;
                console.log(`Stashy Premium: Notes feature check - used: ${usageCounters.notes}, limit: ${FREE_LIMITS.MAX_NOTES}, available: ${isAvailable}`);
                return isAvailable;
            case PREMIUM_FEATURES.UNLIMITED_NOTEBOOKS:
                isAvailable = usageCounters.notebooks < FREE_LIMITS.MAX_NOTEBOOKS;
                console.log(`Stashy Premium: Notebooks feature check - used: ${usageCounters.notebooks}, limit: ${FREE_LIMITS.MAX_NOTEBOOKS}, available: ${isAvailable}`);
                return isAvailable;
            default:
                // All other features require premium or trial
                console.log(`Stashy Premium: Feature '${feature}' blocked - requires premium or trial`);
                return false;
        }
    }

    /**
     * Check if user can perform an action (with usage limits)
     * @param {string} action - Action type ('note', 'notebook', 'highlight')
     * @returns {boolean} True if action is allowed
     */
    function canPerformAction(action) {
        console.log(`Stashy Premium: Checking action permission for '${action}'`);
        console.log(`Stashy Premium: Current usage - notes: ${usageCounters.notes}, notebooks: ${usageCounters.notebooks}, highlights: ${usageCounters.highlights}`);

        if (currentPremiumStatus.isPremium) {
            console.log(`Stashy Premium: Action '${action}' allowed - premium user`);
            return true;
        }

        if (currentTrialStatus.isTrialActive) {
            console.log(`Stashy Premium: Action '${action}' allowed - trial user`);
            return true;
        }

        let canPerform = false;
        switch (action) {
            case 'note':
                canPerform = usageCounters.notes < FREE_LIMITS.MAX_NOTES;
                console.log(`Stashy Premium: Note action check - used: ${usageCounters.notes}/${FREE_LIMITS.MAX_NOTES}, allowed: ${canPerform}`);
                return canPerform;
            case 'notebook':
                canPerform = usageCounters.notebooks < FREE_LIMITS.MAX_NOTEBOOKS;
                console.log(`Stashy Premium: Notebook action check - used: ${usageCounters.notebooks}/${FREE_LIMITS.MAX_NOTEBOOKS}, allowed: ${canPerform}`);
                return canPerform;
            case 'highlight':
                canPerform = usageCounters.highlights < FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE;
                console.log(`Stashy Premium: Highlight action check - used: ${usageCounters.highlights}/${FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE}, allowed: ${canPerform}`);
                return canPerform;
            default:
                console.log(`Stashy Premium: Unknown action '${action}' - denied`);
                return false;
        }
    }

    /**
     * Record usage of a feature (for free tier limits)
     * @param {string} action - Action type ('note', 'notebook', 'highlight')
     */
    async function recordUsage(action) {
        console.log(`Stashy Premium: Recording usage for action '${action}'`);

        if (currentPremiumStatus.isPremium) {
            console.log(`Stashy Premium: Usage not recorded - premium user has unlimited access`);
            return; // No limits for premium users
        }

        if (currentTrialStatus.isTrialActive) {
            console.log(`Stashy Premium: Usage not recorded - trial user has unlimited access`);
            return; // No limits for trial users
        }

        const previousCount = usageCounters[action] || 0;

        switch (action) {
            case 'note':
                usageCounters.notes++;
                console.log(`Stashy Premium: Notes usage updated: ${previousCount} -> ${usageCounters.notes}/${FREE_LIMITS.MAX_NOTES}`);
                break;
            case 'notebook':
                usageCounters.notebooks++;
                console.log(`Stashy Premium: Notebooks usage updated: ${previousCount} -> ${usageCounters.notebooks}/${FREE_LIMITS.MAX_NOTEBOOKS}`);
                break;
            case 'highlight':
                usageCounters.highlights++;
                console.log(`Stashy Premium: Highlights usage updated: ${previousCount} -> ${usageCounters.highlights}/${FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE}`);
                break;
            default:
                console.warn(`Stashy Premium: Unknown action type for usage recording: '${action}'`);
                return;
        }

        // Save updated counters
        try {
            await chrome.storage.local.set({ stashy_usage_counters: usageCounters });
            console.log(`Stashy Premium: Usage counters saved successfully`);

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyUsageCountersChanged', {
                detail: { action, newCount: usageCounters[action], usageCounters }
            }));
        } catch (error) {
            console.error(`Stashy Premium: Error saving usage counters:`, error);
        }
    }

    /**
     * Get current usage statistics
     * @returns {Object} Usage statistics
     */
    function getUsageStats() {
        // For premium and trial users, show unlimited
        if (currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive) {
            return {
                notes: {
                    used: usageCounters.notes,
                    limit: '∞',
                    remaining: '∞',
                    unlimited: true
                },
                notebooks: {
                    used: usageCounters.notebooks,
                    limit: '∞',
                    remaining: '∞',
                    unlimited: true
                },
                highlights: {
                    used: usageCounters.highlights,
                    limit: '∞',
                    remaining: '∞',
                    unlimited: true
                }
            };
        }

        // For free users, show actual limits
        return {
            notes: {
                used: usageCounters.notes,
                limit: FREE_LIMITS.MAX_NOTES,
                remaining: Math.max(0, FREE_LIMITS.MAX_NOTES - usageCounters.notes),
                unlimited: false
            },
            notebooks: {
                used: usageCounters.notebooks,
                limit: FREE_LIMITS.MAX_NOTEBOOKS,
                remaining: Math.max(0, FREE_LIMITS.MAX_NOTEBOOKS - usageCounters.notebooks),
                unlimited: false
            },
            highlights: {
                used: usageCounters.highlights,
                limit: FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE,
                remaining: Math.max(0, FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE - usageCounters.highlights),
                unlimited: false
            }
        };
    }

    /**
     * Get current usage counts (simplified version for dashboard)
     * @returns {Object} Simple usage counts
     */
    function getUsageCounts() {
        return {
            notes: usageCounters.notes,
            notebooks: usageCounters.notebooks,
            highlights: usageCounters.highlights
        };
    }

    /**
     * Get premium status object
     * @returns {Object} Premium status with helper functions
     */
    function getPremiumStatus() {
        return {
            isPremium: currentPremiumStatus.isPremium,
            expiryDate: currentPremiumStatus.expiryDate,
            lastChecked: currentPremiumStatus.lastChecked,
            isFeatureAvailable: isFeatureAvailable,
            usageCounts: getUsageCounts(),
            // Trial information
            isTrialActive: currentTrialStatus.isTrialActive,
            trialExpiryDate: currentTrialStatus.trialExpiryDate,
            trialUsed: currentTrialStatus.trialUsed
        };
    }

    // === TRIAL MANAGEMENT FUNCTIONS ===

    /**
     * Activate free trial for the user
     * @returns {Promise<Object>} Trial activation result
     */
    async function activateTrial() {
        console.log('Stashy Premium: Attempting to activate trial');

        // Check if trial has already been used
        if (currentTrialStatus.trialUsed) {
            console.log('Stashy Premium: Trial already used - cannot activate again');
            return {
                success: false,
                error: 'Trial has already been used',
                code: 'TRIAL_ALREADY_USED'
            };
        }

        // Check if user is already premium
        if (currentPremiumStatus.isPremium) {
            console.log('Stashy Premium: User is already premium - trial not needed');
            return {
                success: false,
                error: 'User already has premium access',
                code: 'ALREADY_PREMIUM'
            };
        }

        // Check if trial is already active
        if (currentTrialStatus.isTrialActive) {
            console.log('Stashy Premium: Trial is already active');
            return {
                success: false,
                error: 'Trial is already active',
                code: 'TRIAL_ALREADY_ACTIVE'
            };
        }

        try {
            const now = Date.now();
            const expiryDate = now + TRIAL_CONFIG.DURATION_MS;

            // Update trial status
            currentTrialStatus = {
                isTrialActive: true,
                trialStartDate: now,
                trialExpiryDate: expiryDate,
                trialUsed: true,
                trialActivatedAt: now
            };

            // Save to storage
            await chrome.storage.local.set({ stashy_trial_status: currentTrialStatus });

            console.log('Stashy Premium: Trial activated successfully', currentTrialStatus);

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyTrialActivated', {
                detail: {
                    trialExpiryDate: expiryDate,
                    daysRemaining: TRIAL_CONFIG.DURATION_DAYS
                }
            }));

            // Schedule trial notifications
            scheduleTrialNotifications();

            return {
                success: true,
                trialExpiryDate: expiryDate,
                daysRemaining: TRIAL_CONFIG.DURATION_DAYS,
                message: `7-day free trial activated! Enjoy unlimited access to all premium features.`
            };

        } catch (error) {
            console.error('Stashy Premium: Error activating trial:', error);
            return {
                success: false,
                error: 'Failed to activate trial',
                code: 'ACTIVATION_ERROR'
            };
        }
    }

    /**
     * Validate current trial status and expire if needed
     */
    async function validateTrialStatus() {
        if (!currentTrialStatus.isTrialActive) {
            return;
        }

        const now = Date.now();
        const timeRemaining = currentTrialStatus.trialExpiryDate - now;

        console.log(`Stashy Premium: Validating trial - time remaining: ${timeRemaining}ms`);

        if (timeRemaining <= 0) {
            console.log('Stashy Premium: Trial has expired - deactivating');
            await expireTrial();
        } else {
            // Check for notification thresholds
            checkTrialNotifications(timeRemaining);
        }
    }

    /**
     * Expire the trial and revert to free tier
     */
    async function expireTrial() {
        console.log('Stashy Premium: Expiring trial');

        currentTrialStatus.isTrialActive = false;

        try {
            // Save updated status
            await chrome.storage.local.set({ stashy_trial_status: currentTrialStatus });

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyTrialExpired', {
                detail: {
                    trialUsed: currentTrialStatus.trialUsed,
                    message: 'Your 7-day free trial has expired. Upgrade to Stashy Pro to continue enjoying premium features!'
                }
            }));

            // Show trial expiry notification
            showTrialExpiryNotification();

            console.log('Stashy Premium: Trial expired successfully');
        } catch (error) {
            console.error('Stashy Premium: Error expiring trial:', error);
        }
    }

    /**
     * Get trial status information
     * @returns {Object} Trial status details
     */
    function getTrialStatus() {
        if (!currentTrialStatus.isTrialActive) {
            return {
                isActive: false,
                isUsed: currentTrialStatus.trialUsed,
                canActivate: !currentTrialStatus.trialUsed && !currentPremiumStatus.isPremium,
                daysRemaining: 0,
                hoursRemaining: 0,
                timeRemaining: 0
            };
        }

        const now = Date.now();
        const timeRemaining = Math.max(0, currentTrialStatus.trialExpiryDate - now);
        const daysRemaining = Math.ceil(timeRemaining / (24 * 60 * 60 * 1000));
        const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));

        return {
            isActive: true,
            isUsed: currentTrialStatus.trialUsed,
            canActivate: false,
            daysRemaining,
            hoursRemaining,
            timeRemaining,
            expiryDate: currentTrialStatus.trialExpiryDate,
            startDate: currentTrialStatus.trialStartDate
        };
    }

    /**
     * Check if trial notifications should be shown
     * @param {number} timeRemaining - Time remaining in milliseconds
     */
    function checkTrialNotifications(timeRemaining) {
        const thresholds = TRIAL_CONFIG.NOTIFICATION_THRESHOLDS;

        // Check for 3-day warning
        if (timeRemaining <= thresholds.THREE_DAYS && timeRemaining > thresholds.ONE_DAY) {
            showTrialNotification('3-day-warning', {
                title: 'Trial Ending Soon',
                message: 'Your free trial expires in 3 days. Upgrade to Stashy Pro to keep your premium features!',
                daysRemaining: 3
            });
        }

        // Check for 1-day warning
        else if (timeRemaining <= thresholds.ONE_DAY && timeRemaining > 0) {
            showTrialNotification('1-day-warning', {
                title: 'Trial Expires Tomorrow',
                message: 'Your free trial expires in 1 day. Upgrade now to avoid losing access to premium features!',
                daysRemaining: 1
            });
        }
    }

    /**
     * Schedule trial notifications
     */
    function scheduleTrialNotifications() {
        console.log('Stashy Premium: Scheduling trial notifications');

        // Set up periodic checks for trial expiry
        setInterval(() => {
            if (currentTrialStatus.isTrialActive) {
                validateTrialStatus();
            }
        }, 60 * 60 * 1000); // Check every hour
    }

    /**
     * Show trial notification
     * @param {string} type - Notification type
     * @param {Object} data - Notification data
     */
    function showTrialNotification(type, data) {
        console.log(`Stashy Premium: Showing trial notification - ${type}`, data);

        // Dispatch event for UI handling
        document.dispatchEvent(new CustomEvent('stashyTrialNotification', {
            detail: { type, ...data }
        }));

        // Show notification using existing system
        if (typeof window.showStatus === 'function') {
            window.showStatus(data.message, 'warning', 8000);
        }
    }

    /**
     * Show trial expiry notification with conversion prompt
     */
    function showTrialExpiryNotification() {
        console.log('Stashy Premium: Showing trial expiry notification');

        const message = 'Your 7-day free trial has expired. Upgrade to Stashy Pro to continue enjoying unlimited notes, AI features, and more!';

        // Show upgrade prompt
        if (typeof showUpgradePrompt === 'function') {
            showUpgradePrompt('Trial Expired - Upgrade Now', 'trial-expiry');
        } else {
            // Fallback notification
            if (typeof window.showStatus === 'function') {
                window.showStatus(message, 'error', 10000);
            }
        }
    }

    /**
     * Refresh premium status from background script
     * @param {boolean} showLoading - Whether to show loading indicator
     */
    async function refreshPremiumStatus(showLoading = false) {
        console.log('Stashy Premium: Refreshing premium status...');

        if (showLoading) {
            // Dispatch loading event
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusLoading', {
                detail: { isLoading: true }
            }));
        }

        try {
            const status = await chrome.runtime.sendMessage({ action: 'getPremiumStatus' });

            currentPremiumStatus = {
                isPremium: status.isPremium || false,
                expiryDate: status.expiryDate || null,
                lastChecked: Date.now()
            };

            // Save to storage
            await chrome.storage.local.set({ stashy_premium_status: currentPremiumStatus });

            console.log('Stashy Premium: Status refreshed successfully', currentPremiumStatus);

            // Dispatch success event for UI updates
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusChanged', {
                detail: { ...currentPremiumStatus, success: true }
            }));

            return currentPremiumStatus;
        } catch (error) {
            console.error('Stashy Premium: Error refreshing status:', error);

            // Dispatch error event
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusError', {
                detail: {
                    error: error.message || 'Failed to refresh premium status',
                    fallbackStatus: currentPremiumStatus
                }
            }));

            return currentPremiumStatus;
        } finally {
            if (showLoading) {
                // Dispatch loading complete event
                document.dispatchEvent(new CustomEvent('stashyPremiumStatusLoading', {
                    detail: { isLoading: false }
                }));
            }
        }
    }

    /**
     * Show upgrade prompt for locked features
     * @param {string} feature - Feature that requires upgrade
     * @param {string} context - Context where the prompt is shown
     */
    function showUpgradePrompt(feature, context = '') {
        console.log(`Stashy Premium: Showing upgrade prompt for feature '${feature}' in context '${context}'`);

        const upgradeUrl = 'https://stashyapp.com/pricing';

        // Create upgrade notification
        const message = `This feature requires Stashy Pro. Upgrade to unlock ${feature} and more!`;

        console.log(`Stashy Premium: Upgrade message: ${message}`);
        console.log(`Stashy Premium: Upgrade URL: ${upgradeUrl}`);

        // Show notification (you can customize this based on your UI)
        if (typeof window.StashyUI !== 'undefined' && window.StashyUI.showNotification) {
            console.log(`Stashy Premium: Using StashyUI notification system`);
            window.StashyUI.showNotification(message, 'upgrade', {
                action: 'Upgrade Now',
                callback: () => {
                    console.log(`Stashy Premium: Opening upgrade URL: ${upgradeUrl}`);
                    window.open(upgradeUrl, '_blank');
                }
            });
        } else {
            // Fallback to alert
            console.log(`Stashy Premium: Using fallback alert dialog`);
            if (confirm(`${message}\n\nWould you like to upgrade now?`)) {
                console.log(`Stashy Premium: User confirmed upgrade, opening URL: ${upgradeUrl}`);
                try {
                    window.open(upgradeUrl, '_blank');
                    console.log(`Stashy Premium: Successfully opened upgrade URL`);
                } catch (error) {
                    console.error(`Stashy Premium: Error opening upgrade URL:`, error);
                    // Fallback: try to navigate current window
                    try {
                        window.location.href = upgradeUrl;
                    } catch (navError) {
                        console.error(`Stashy Premium: Error navigating to upgrade URL:`, navError);
                    }
                }
            } else {
                console.log(`Stashy Premium: User cancelled upgrade prompt`);
            }
        }
    }

    /**
     * Apply premium styling to UI elements
     * @param {boolean} isPremiumUser - Whether user is premium
     */
    function applyPremiumStyling(isPremiumUser) {
        console.log(`Stashy Premium: Applying premium styling, isPremiumUser: ${isPremiumUser}`);

        const body = document.body;

        if (isPremiumUser) {
            body.classList.add('stashy-premium-user');
            body.classList.remove('stashy-free-user');
            console.log(`Stashy Premium: Applied premium user styling`);
        } else {
            body.classList.add('stashy-free-user');
            body.classList.remove('stashy-premium-user');
            console.log(`Stashy Premium: Applied free user styling`);
        }

        // Update all premium feature elements
        const premiumElements = document.querySelectorAll('[data-premium-feature]');
        console.log(`Stashy Premium: Found ${premiumElements.length} premium feature elements to update`);

        premiumElements.forEach((element, index) => {
            const feature = element.getAttribute('data-premium-feature');
            const isAvailable = isFeatureAvailable(feature);

            console.log(`Stashy Premium: Element ${index + 1} - feature: '${feature}', available: ${isAvailable}`);

            element.classList.toggle('premium-locked', !isAvailable);
            element.classList.toggle('premium-available', isAvailable);

            if (!isAvailable) {
                element.setAttribute('title', 'This feature requires Stashy Pro');
            } else {
                element.removeAttribute('title');
            }
        });
    }

    /**
     * Validate free tier limits are being enforced correctly
     * @returns {Object} Validation results
     */
    function validateFreeTierLimits() {
        console.log(`Stashy Premium: Validating free tier limits enforcement`);

        const validation = {
            isValid: true,
            issues: [],
            currentUsage: { ...usageCounters },
            limits: { ...FREE_LIMITS },
            isPremium: currentPremiumStatus.isPremium
        };

        // Check if usage counters exceed limits for free users
        if (!currentPremiumStatus.isPremium) {
            if (usageCounters.notes > FREE_LIMITS.MAX_NOTES) {
                validation.isValid = false;
                validation.issues.push(`Notes usage (${usageCounters.notes}) exceeds free limit (${FREE_LIMITS.MAX_NOTES})`);
            }

            if (usageCounters.notebooks > FREE_LIMITS.MAX_NOTEBOOKS) {
                validation.isValid = false;
                validation.issues.push(`Notebooks usage (${usageCounters.notebooks}) exceeds free limit (${FREE_LIMITS.MAX_NOTEBOOKS})`);
            }

            if (usageCounters.highlights > FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE) {
                validation.isValid = false;
                validation.issues.push(`Highlights usage (${usageCounters.highlights}) exceeds free limit (${FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE})`);
            }
        }

        console.log(`Stashy Premium: Validation result:`, validation);
        return validation;
    }

    /**
     * Reset usage counters (for testing purposes)
     */
    async function resetUsageCounters() {
        console.log(`Stashy Premium: Resetting usage counters`);

        usageCounters = {
            notes: 0,
            notebooks: 0,
            highlights: 0
        };

        try {
            await chrome.storage.local.set({ stashy_usage_counters: usageCounters });
            console.log(`Stashy Premium: Usage counters reset successfully`);

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyUsageCountersReset', {
                detail: { usageCounters }
            }));
        } catch (error) {
            console.error(`Stashy Premium: Error resetting usage counters:`, error);
        }
    }

    // --- Safe Premium Check Utilities ---
    function safeFeatureCheck(featureId, fallbackValue = false) {
        try {
            return isFeatureAvailable(featureId);
        } catch (error) {
            console.warn(`Stashy Premium: Error checking feature ${featureId}:`, error);
            showConsistentErrorMessage('Feature Check Failed', `Unable to verify access to ${featureId}. Please try again.`);
            return fallbackValue;
        }
    }

    function safeActionCheck(actionType, fallbackValue = true) {
        try {
            return canPerformAction(actionType);
        } catch (error) {
            console.warn(`Stashy Premium: Error checking action ${actionType}:`, error);
            showConsistentErrorMessage('Action Check Failed', `Unable to verify permission for ${actionType}. Please try again.`);
            return fallbackValue; // Default to allowing action if check fails
        }
    }

    function safeUsageRecord(actionType) {
        try {
            return recordUsage(actionType);
        } catch (error) {
            console.warn(`Stashy Premium: Error recording usage for ${actionType}:`, error);
            showConsistentErrorMessage('Usage Recording Failed', `Unable to record usage for ${actionType}. Your limits may not be accurate.`);
            return Promise.resolve(); // Return resolved promise to prevent blocking
        }
    }

    function safePremiumStatus() {
        try {
            return refreshPremiumStatus();
        } catch (error) {
            console.warn('Stashy Premium: Error getting premium status:', error);
            showConsistentErrorMessage('Premium Status Check Failed', 'Unable to verify your premium status. Some features may be temporarily unavailable.');
            return Promise.resolve({
                isPremium: false,
                expiryDate: null,
                lastChecked: 0
            });
        }
    }

    function safeUpgradePrompt(feature, context) {
        try {
            return showUpgradePrompt(feature, context);
        } catch (error) {
            console.warn('Stashy Premium: Error showing upgrade prompt:', error);
            showConsistentErrorMessage('Upgrade Prompt Failed', 'Unable to show upgrade options. Please visit stashyapp.com/pricing directly.');
            // Fallback: open stashyapp.com directly
            try {
                window.open('https://stashyapp.com/pricing', '_blank');
            } catch (openError) {
                console.error('Stashy Premium: Failed to open upgrade page:', openError);
            }
        }
    }

    /**
     * Show consistent error messages across all premium features
     * @param {string} title - Error title
     * @param {string} message - Error message
     */
    function showConsistentErrorMessage(title, message) {
        console.error(`Stashy Premium: ${title} - ${message}`);

        // Try to use existing notification system
        if (typeof window.showStatus === 'function') {
            window.showStatus(`${title}: ${message}`, 'error', 5000);
        } else if (typeof window.StashyUI !== 'undefined' && window.StashyUI.showNotification) {
            window.StashyUI.showNotification(message, 'error', { title });
        } else {
            // Fallback to console and simple alert for critical errors
            console.warn(`Stashy Premium: Showing fallback error dialog for: ${title}`);
            // Only show alert for critical errors to avoid spam
            if (title.includes('Premium Status') || title.includes('Feature Check')) {
                setTimeout(() => {
                    alert(`Stashy Premium Error\n\n${title}\n${message}`);
                }, 100);
            }
        }
    }

    /**
     * Apply accessibility attributes to premium UI elements
     */
    function applyAccessibilityAttributes() {
        console.log('Stashy Premium: Applying accessibility attributes');

        // Update premium feature elements with proper ARIA attributes
        const premiumElements = document.querySelectorAll('[data-premium-feature]');
        premiumElements.forEach(element => {
            const feature = element.getAttribute('data-premium-feature');
            const isAvailable = isFeatureAvailable(feature);

            // Add ARIA attributes
            element.setAttribute('aria-label', `${feature} feature`);
            element.setAttribute('aria-describedby', `${feature}-status`);

            if (!isAvailable) {
                element.setAttribute('aria-disabled', 'true');
                element.setAttribute('role', 'button');
                element.setAttribute('tabindex', '0');

                // Add keyboard support for upgrade prompts
                element.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        showUpgradePrompt(feature, 'keyboard-navigation');
                    }
                });
            } else {
                element.removeAttribute('aria-disabled');
                element.removeAttribute('tabindex');
            }
        });

        // Update usage counters with ARIA labels
        const usageElements = document.querySelectorAll('[data-usage-counter]');
        usageElements.forEach(element => {
            const counterType = element.getAttribute('data-usage-counter');
            const stats = getUsageStats();

            if (stats[counterType]) {
                const { used, limit, remaining } = stats[counterType];
                element.setAttribute('aria-label', `${counterType} usage: ${used} of ${limit} used, ${remaining} remaining`);

                if (remaining <= 1) {
                    element.setAttribute('aria-describedby', `${counterType}-warning`);
                }
            }
        });
    }

    // Public API
    return {
        initialize,
        isPremium,
        isFeatureAvailable,
        canPerformAction,
        recordUsage,
        getUsageStats,
        getUsageCounts,
        getPremiumStatus,
        refreshPremiumStatus,
        showUpgradePrompt,
        applyPremiumStyling,
        applyAccessibilityAttributes,
        validateFreeTierLimits,
        resetUsageCounters,
        showConsistentErrorMessage,
        PREMIUM_FEATURES,
        FREE_LIMITS,
        // Trial functions
        isTrialActive,
        isTrialUsed,
        activateTrial,
        getTrialStatus,
        validateTrialStatus,
        expireTrial,
        TRIAL_CONFIG,
        // Safe utility functions
        safeFeatureCheck,
        safeActionCheck,
        safeUsageRecord,
        safePremiumStatus,
        safeUpgradePrompt
    };
})();

// Initialize when loaded
if (typeof chrome !== 'undefined' && chrome.runtime) {
    window.StashyPremium.initialize().catch(console.error);
}

console.log('Stashy: Premium Manager Loaded');
